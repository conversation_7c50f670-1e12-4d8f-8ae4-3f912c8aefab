#!/usr/bin/env python3
"""
3D体素可视化脚本
用于可视化Rust生成的3D多分辨率网格数据
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from matplotlib.patches import Rectangle
import matplotlib.patches as patches
from mpl_toolkits.mplot3d.art3d import Poly3DCollection

def load_voxel_data(filename):
    """加载体素数据"""
    with open(filename, 'r') as f:
        data = json.load(f)
    return data

def create_voxel_array(voxel_data):
    """将一维体素数据转换为3D数组"""
    size = voxel_data['size']
    voxels_1d = voxel_data['voxels']
    
    # 转换为3D数组 (z, y, x)
    voxels_3d = np.array(voxels_1d).reshape((size, size, size))
    
    return voxels_3d

def draw_grid_boundary(ax, boundary):
    """绘制网格边界框"""
    x, y, z = boundary['x'], boundary['y'], boundary['z']
    w, h, d = boundary['width'], boundary['height'], boundary['depth']
    
    # 定义立方体的8个顶点
    vertices = [
        [x, y, z], [x+w, y, z], [x+w, y+h, z], [x, y+h, z],  # 底面
        [x, y, z+d], [x+w, y, z+d], [x+w, y+h, z+d], [x, y+h, z+d]  # 顶面
    ]
    
    # 定义立方体的12条边
    edges = [
        [0, 1], [1, 2], [2, 3], [3, 0],  # 底面
        [4, 5], [5, 6], [6, 7], [7, 4],  # 顶面
        [0, 4], [1, 5], [2, 6], [3, 7]   # 垂直边
    ]
    
    # 根据网格类型选择颜色
    if boundary['node_type'] == 'Empty':
        color = 'green'
        alpha = 0.3
    elif boundary['node_type'] == 'Blocked':
        color = 'red'
        alpha = 0.3
    else:  # Mixed
        color = 'blue'
        alpha = 0.3
    
    # 绘制边框
    for edge in edges:
        points = [vertices[edge[0]], vertices[edge[1]]]
        ax.plot3D(*zip(*points), color=color, alpha=alpha, linewidth=1)

def visualize_3d_voxels(voxel_data, show_grid_boundaries=True, show_slice=None):
    """可视化3D体素数据"""
    voxels_3d = create_voxel_array(voxel_data)
    size = voxel_data['size']
    
    fig = plt.figure(figsize=(15, 12))
    
    if show_slice is not None:
        # 显示指定Z层的切片
        ax1 = fig.add_subplot(121)
        slice_data = voxels_3d[show_slice, :, :]
        
        # 创建颜色映射
        colors = np.zeros((*slice_data.shape, 4))
        colors[slice_data == 1] = [0, 1, 0, 0.6]    # 绿色 - 空旷网格
        colors[slice_data == 2] = [1, 0, 0, 0.6]    # 红色 - 被占据网格
        colors[slice_data == 3] = [0, 0, 0, 0.9]    # 黑色 - 障碍物
        
        ax1.imshow(colors, origin='lower')
        ax1.set_title(f'Z={show_slice} 层切片')
        ax1.set_xlabel('X')
        ax1.set_ylabel('Y')
        
        # 3D视图
        ax2 = fig.add_subplot(122, projection='3d')
    else:
        # 只显示3D视图
        ax2 = fig.add_subplot(111, projection='3d')
    
    # 创建3D体素显示
    # 只显示非空体素以提高性能
    filled = voxels_3d > 0
    
    # 创建颜色数组
    colors = np.empty(filled.shape + (4,))
    colors[voxels_3d == 1] = [0, 1, 0, 0.3]    # 绿色半透明 - 空旷网格
    colors[voxels_3d == 2] = [1, 0, 0, 0.3]    # 红色半透明 - 被占据网格
    colors[voxels_3d == 3] = [0, 0, 0, 0.8]    # 黑色 - 障碍物
    
    # 绘制体素
    ax2.voxels(filled, facecolors=colors, edgecolors='k', alpha=0.7, linewidth=0.1)
    
    # 绘制网格边界
    if show_grid_boundaries:
        print("绘制网格边界...")
        boundaries = voxel_data['grid_boundaries']
        # 只绘制部分边界以避免过于复杂
        for i, boundary in enumerate(boundaries[:50]):  # 限制显示数量
            draw_grid_boundary(ax2, boundary)
    
    ax2.set_xlabel('X')
    ax2.set_ylabel('Y')
    ax2.set_zlabel('Z')
    ax2.set_title('3D多分辨率网格可视化')
    
    # 设置相等的坐标轴比例
    ax2.set_xlim(0, size)
    ax2.set_ylim(0, size)
    ax2.set_zlim(0, size)
    
    plt.tight_layout()
    return fig

def create_slice_animation(voxel_data, output_filename='3d_slices.gif'):
    """创建Z轴切片动画"""
    voxels_3d = create_voxel_array(voxel_data)
    size = voxel_data['size']
    
    fig, ax = plt.subplots(figsize=(10, 10))
    
    images = []
    for z in range(0, size, 2):  # 每隔2层显示一次
        slice_data = voxels_3d[z, :, :]
        
        # 创建颜色映射
        colors = np.zeros((*slice_data.shape, 4))
        colors[slice_data == 1] = [0, 1, 0, 0.6]    # 绿色 - 空旷网格
        colors[slice_data == 2] = [1, 0, 0, 0.6]    # 红色 - 被占据网格
        colors[slice_data == 3] = [0, 0, 0, 0.9]    # 黑色 - 障碍物
        
        ax.clear()
        ax.imshow(colors, origin='lower')
        ax.set_title(f'Z={z} 层切片 (共{size}层)')
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        
        plt.savefig(f'temp_slice_{z:03d}.png', dpi=100, bbox_inches='tight')
    
    print(f"切片图像已保存，可以使用工具制作动画")

def main():
    """主函数"""
    try:
        print("加载3D体素数据...")
        voxel_data = load_voxel_data('voxel_data_3d.json')
        
        print(f"数据大小: {voxel_data['size']}x{voxel_data['size']}x{voxel_data['size']}")
        print(f"网格边界数量: {len(voxel_data['grid_boundaries'])}")
        
        # 统计体素类型
        voxels = np.array(voxel_data['voxels'])
        empty_count = np.sum(voxels == 0)
        grid_empty_count = np.sum(voxels == 1)
        grid_blocked_count = np.sum(voxels == 2)
        obstacle_count = np.sum(voxels == 3)
        
        print(f"体素统计:")
        print(f"  空白: {empty_count}")
        print(f"  空旷网格: {grid_empty_count}")
        print(f"  被占据网格: {grid_blocked_count}")
        print(f"  障碍物: {obstacle_count}")
        
        # 创建可视化
        print("创建3D可视化...")
        
        # 选择1: 完整3D视图
        fig1 = visualize_3d_voxels(voxel_data, show_grid_boundaries=True)
        plt.savefig('3d_visualization_full.png', dpi=150, bbox_inches='tight')
        print("完整3D视图已保存为 3d_visualization_full.png")
        
        # 选择2: 带切片的视图
        fig2 = visualize_3d_voxels(voxel_data, show_grid_boundaries=False, show_slice=32)
        plt.savefig('3d_visualization_with_slice.png', dpi=150, bbox_inches='tight')
        print("带切片的3D视图已保存为 3d_visualization_with_slice.png")
        
        # 选择3: 创建切片序列
        print("创建切片序列...")
        create_slice_animation(voxel_data)
        
        plt.show()
        
    except FileNotFoundError:
        print("错误: 找不到 voxel_data_3d.json 文件")
        print("请先运行Rust程序生成数据文件")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()
