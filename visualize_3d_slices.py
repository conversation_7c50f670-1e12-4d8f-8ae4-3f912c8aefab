#!/usr/bin/env python3
"""
3D切片可视化脚本
将3D数据按Z轴切片，生成类似2D可视化的PNG图像
每个切片显示：网格边框（绿色/红色）+ 障碍物填充（黑色）+ 透明背景
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Rectangle
import os

def load_voxel_data(filename):
    """加载体素数据"""
    with open(filename, 'r') as f:
        data = json.load(f)
    return data

def create_voxel_array(voxel_data):
    """将一维体素数据转换为3D数组"""
    size = voxel_data['size']
    voxels_1d = voxel_data['voxels']
    
    # 转换为3D数组 (z, y, x)
    voxels_3d = np.array(voxels_1d).reshape((size, size, size))
    
    return voxels_3d

def get_slice_boundaries(boundaries, z_level):
    """获取指定Z层的网格边界"""
    slice_boundaries = []
    
    for boundary in boundaries:
        z_start = boundary["z"]
        z_end = boundary["z"] + boundary["depth"]
        
        # 检查边界是否与当前Z层相交
        if z_start <= z_level < z_end:
            slice_boundaries.append(boundary)
    
    return slice_boundaries

def draw_grid_boundary_2d(ax, boundary, z_level):
    """在2D图上绘制网格边界矩形"""
    x = boundary["x"]
    y = boundary["y"]
    width = boundary["width"]
    height = boundary["height"]
    
    # 根据网格类型选择颜色
    if boundary["node_type"] == "Empty":
        color = "green"
        alpha = 0.8
        linewidth = 2
    elif boundary["node_type"] == "Blocked":
        color = "red"
        alpha = 0.8
        linewidth = 2
    else:  # Mixed - 不显示
        return
    
    # 绘制矩形边框，不填充
    rect = Rectangle((x, y), width, height, 
                    linewidth=linewidth, 
                    edgecolor=color, 
                    facecolor='none',  # 不填充
                    alpha=alpha)
    ax.add_patch(rect)

def visualize_slice(voxel_data, z_level, save_path=None):
    """可视化指定Z层的切片"""
    voxels_3d = create_voxel_array(voxel_data)
    size = voxel_data['size']
    
    # 获取当前Z层的数据
    slice_data = voxels_3d[z_level, :, :]
    
    # 创建图像
    fig, ax = plt.subplots(figsize=(10, 10))
    
    # 设置透明背景
    fig.patch.set_alpha(0)
    ax.patch.set_alpha(0)
    
    # 显示障碍物（黑色填充）
    obstacle_mask = slice_data == 3
    if np.any(obstacle_mask):
        # 创建障碍物图像
        obstacle_image = np.zeros((size, size, 4))  # RGBA
        obstacle_image[obstacle_mask] = [0, 0, 0, 1]  # 黑色不透明
        ax.imshow(obstacle_image, origin='lower', extent=[0, size, 0, size])
    
    # 绘制网格边界
    boundaries = voxel_data['grid_boundaries']
    slice_boundaries = get_slice_boundaries(boundaries, z_level)
    
    green_count = 0
    red_count = 0
    
    for boundary in slice_boundaries:
        if boundary["node_type"] == "Empty":
            draw_grid_boundary_2d(ax, boundary, z_level)
            green_count += 1
        elif boundary["node_type"] == "Blocked":
            draw_grid_boundary_2d(ax, boundary, z_level)
            red_count += 1
    
    # 设置坐标轴
    ax.set_xlim(0, size)
    ax.set_ylim(0, size)
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_title(f'Z={z_level} 层切片 (绿色:{green_count}个, 红色:{red_count}个网格)')
    ax.grid(True, alpha=0.3)
    ax.set_aspect('equal')
    
    # 添加图例
    from matplotlib.lines import Line2D
    legend_elements = [
        Line2D([0], [0], color='green', lw=2, label='空旷网格边框'),
        Line2D([0], [0], color='red', lw=2, label='被占据网格边框'),
        Line2D([0], [0], color='black', lw=4, label='障碍物')
    ]
    ax.legend(handles=legend_elements, loc='upper right')
    
    plt.tight_layout()
    
    # 保存图像
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        print(f"切片 Z={z_level} 已保存到 {save_path}")
    
    return fig

def create_all_slices(voxel_data, output_dir='slices'):
    """创建所有Z层的切片图像"""
    size = voxel_data['size']
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    print(f"开始生成 {size} 个切片图像...")
    
    # 统计每层的障碍物数量
    voxels_3d = create_voxel_array(voxel_data)
    
    for z in range(size):
        slice_data = voxels_3d[z, :, :]
        obstacle_count = np.sum(slice_data == 3)
        
        # 只为有内容的层生成图像（有障碍物或网格边界）
        boundaries = voxel_data['grid_boundaries']
        slice_boundaries = get_slice_boundaries(boundaries, z)
        
        if obstacle_count > 0 or len(slice_boundaries) > 0:
            save_path = os.path.join(output_dir, f'slice_z_{z:02d}.png')
            fig = visualize_slice(voxel_data, z, save_path)
            plt.close(fig)  # 关闭图像以节省内存
        else:
            print(f"跳过空白层 Z={z}")
    
    print(f"所有切片已保存到 {output_dir} 目录")

def create_summary_grid(voxel_data, output_path='slices_summary.png'):
    """创建所有切片的缩略图网格"""
    size = voxel_data['size']
    voxels_3d = create_voxel_array(voxel_data)
    
    # 计算网格布局
    cols = 8  # 每行8个切片
    rows = (size + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=(20, rows * 2.5))
    fig.suptitle(f'所有Z层切片总览 ({size}x{size}x{size})', fontsize=16)
    
    if rows == 1:
        axes = [axes]
    
    for z in range(size):
        row = z // cols
        col = z % cols
        
        if rows > 1:
            ax = axes[row][col]
        else:
            ax = axes[col]
        
        slice_data = voxels_3d[z, :, :]
        
        # 显示障碍物
        obstacle_mask = slice_data == 3
        if np.any(obstacle_mask):
            obstacle_image = np.zeros((size, size, 4))
            obstacle_image[obstacle_mask] = [0, 0, 0, 1]
            ax.imshow(obstacle_image, origin='lower')
        
        # 绘制网格边界（简化版）
        boundaries = voxel_data['grid_boundaries']
        slice_boundaries = get_slice_boundaries(boundaries, z)
        
        for boundary in slice_boundaries:
            if boundary["node_type"] == "Empty":
                color = "green"
            elif boundary["node_type"] == "Blocked":
                color = "red"
            else:
                continue
            
            rect = Rectangle((boundary["x"], boundary["y"]), 
                           boundary["width"], boundary["height"],
                           linewidth=1, edgecolor=color, facecolor='none', alpha=0.6)
            ax.add_patch(rect)
        
        ax.set_title(f'Z={z}', fontsize=10)
        ax.set_xlim(0, size)
        ax.set_ylim(0, size)
        ax.set_xticks([])
        ax.set_yticks([])
    
    # 隐藏多余的子图
    for z in range(size, rows * cols):
        row = z // cols
        col = z % cols
        if rows > 1:
            axes[row][col].set_visible(False)
        else:
            axes[col].set_visible(False)
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    print(f"切片总览已保存到 {output_path}")
    
    return fig

def main():
    """主函数"""
    try:
        print("加载3D体素数据...")
        voxel_data = load_voxel_data('voxel_data_3d.json')
        
        size = voxel_data['size']
        print(f"数据大小: {size}x{size}x{size}")
        print(f"网格边界数量: {len(voxel_data['grid_boundaries'])}")
        
        # 统计体素类型
        voxels = np.array(voxel_data['voxels'])
        obstacle_count = np.sum(voxels == 3)
        print(f"障碍物体素数量: {obstacle_count}")
        
        # 生成所有切片
        create_all_slices(voxel_data)
        
        # 生成切片总览
        fig_summary = create_summary_grid(voxel_data)
        plt.show()
        
        print("\n切片可视化完成！")
        print("- 单个切片保存在 slices/ 目录")
        print("- 总览图保存为 slices_summary.png")
        
    except FileNotFoundError:
        print("错误: 找不到 voxel_data_3d.json 文件")
        print("请先运行Rust程序生成数据文件")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()
