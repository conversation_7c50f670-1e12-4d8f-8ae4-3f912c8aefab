#!/usr/bin/env python3
"""
3D体素可视化脚本 - 简化版
用于可视化Rust生成的3D多分辨率网格数据
只显示一个3D交互式图，重点显示网格边框和障碍物
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.lines import Line2D


def load_voxel_data(filename):
    """加载体素数据"""
    with open(filename, "r") as f:
        data = json.load(f)
    return data


def create_voxel_array(voxel_data):
    """将一维体素数据转换为3D数组"""
    size = voxel_data["size"]
    voxels_1d = voxel_data["voxels"]

    # 转换为3D数组 (z, y, x)
    voxels_3d = np.array(voxels_1d).reshape((size, size, size))

    return voxels_3d


def draw_grid_boundary(ax, boundary):
    """绘制网格边界框 - 只显示绿色和红色"""
    x, y, z = boundary["x"], boundary["y"], boundary["z"]
    w, h, d = boundary["width"], boundary["height"], boundary["depth"]

    # 定义立方体的8个顶点
    vertices = [
        [x, y, z],
        [x + w, y, z],
        [x + w, y + h, z],
        [x, y + h, z],  # 底面
        [x, y, z + d],
        [x + w, y, z + d],
        [x + w, y + h, z + d],
        [x, y + h, z + d],  # 顶面
    ]

    # 定义立方体的12条边
    edges = [
        [0, 1],
        [1, 2],
        [2, 3],
        [3, 0],  # 底面
        [4, 5],
        [5, 6],
        [6, 7],
        [7, 4],  # 顶面
        [0, 4],
        [1, 5],
        [2, 6],
        [3, 7],  # 垂直边
    ]

    # 根据网格类型选择颜色 - 只显示绿色和红色
    if boundary["node_type"] == "Empty":
        color = "green"
        alpha = 0.8
        linewidth = 2
    elif boundary["node_type"] == "Blocked":
        color = "red"
        alpha = 0.8
        linewidth = 2
    else:  # Mixed - 不显示混合类型的边框
        return

    # 绘制边框
    for edge in edges:
        points = [vertices[edge[0]], vertices[edge[1]]]
        ax.plot3D(*zip(*points), color=color, alpha=alpha, linewidth=linewidth)


def visualize_3d_simple(voxel_data):
    """简化的3D可视化 - 只显示障碍物填充和网格边框线条"""
    voxels_3d = create_voxel_array(voxel_data)
    size = voxel_data["size"]

    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, projection="3d")

    # 设置透明背景
    ax.xaxis.pane.fill = False
    ax.yaxis.pane.fill = False
    ax.zaxis.pane.fill = False

    # 只显示障碍物体素（黑色填充）
    obstacle_voxels = voxels_3d == 3
    if np.any(obstacle_voxels):
        ax.voxels(
            obstacle_voxels,
            facecolors="black",
            alpha=0.9,
            edgecolors="darkgray",
            linewidth=0.5,
        )

    # 绘制网格边界 - 只显示边框线条，不填充
    print("绘制网格边界...")
    boundaries = voxel_data["grid_boundaries"]

    green_count = 0
    red_count = 0

    for boundary in boundaries:
        if boundary["node_type"] == "Empty":
            draw_grid_boundary(ax, boundary)
            green_count += 1
        elif boundary["node_type"] == "Blocked":
            draw_grid_boundary(ax, boundary)
            red_count += 1
        # 忽略Mixed类型

    print(f"显示了 {green_count} 个绿色网格边框（空旷区域）")
    print(f"显示了 {red_count} 个红色网格边框（被占据区域）")

    ax.set_xlabel("X")
    ax.set_ylabel("Y")
    ax.set_zlabel("Z")
    ax.set_title(f"3D多分辨率网格可视化 ({size}x{size}x{size})")

    # 设置坐标轴范围
    ax.set_xlim(0, size)
    ax.set_ylim(0, size)
    ax.set_zlim(0, size)

    # 设置视角
    ax.view_init(elev=20, azim=45)

    # 设置网格线透明
    ax.grid(False)

    # 添加图例
    legend_elements = [
        Line2D([0], [0], color="green", lw=2, label="空旷网格边框"),
        Line2D([0], [0], color="red", lw=2, label="被占据网格边框"),
        Line2D([0], [0], color="black", lw=4, label="障碍物（填充）"),
    ]
    ax.legend(handles=legend_elements, loc="upper right")

    plt.tight_layout()
    return fig


def main():
    """主函数"""
    try:
        print("加载3D体素数据...")
        voxel_data = load_voxel_data("voxel_data_3d.json")

        print(
            f"数据大小: {voxel_data['size']}x{voxel_data['size']}x{voxel_data['size']}"
        )
        print(f"网格边界数量: {len(voxel_data['grid_boundaries'])}")

        # 统计体素类型
        voxels = np.array(voxel_data["voxels"])
        empty_count = np.sum(voxels == 0)
        grid_empty_count = np.sum(voxels == 1)
        grid_blocked_count = np.sum(voxels == 2)
        obstacle_count = np.sum(voxels == 3)

        print(f"体素统计:")
        print(f"  空白: {empty_count}")
        print(f"  空旷网格: {grid_empty_count}")
        print(f"  被占据网格: {grid_blocked_count}")
        print(f"  障碍物: {obstacle_count}")

        # 创建简化的3D可视化
        print("创建3D可视化...")
        fig = visualize_3d_simple(voxel_data)

        # 保存图像
        plt.savefig("3d_visualization_simple.png", dpi=150, bbox_inches="tight")
        print("3D可视化已保存为 3d_visualization_simple.png")

        # 显示交互式图形
        plt.show()

    except FileNotFoundError:
        print("错误: 找不到 voxel_data_3d.json 文件")
        print("请先运行Rust程序生成数据文件")
    except Exception as e:
        print(f"错误: {e}")


if __name__ == "__main__":
    main()
