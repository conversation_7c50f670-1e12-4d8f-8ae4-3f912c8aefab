use image::{<PERSON><PERSON><PERSON><PERSON>, Rgb, RgbImage};
use imageproc::drawing::{draw_hollow_rect_mut, draw_filled_rect_mut, draw_line_segment_mut};
use imageproc::rect::Rect;
use std::collections::{HashMap, BinaryHeap, HashSet};
use std::cmp::Ordering;
use std::time::Instant;
use serde::{Serialize, Deserialize};

/// 节点类型枚举
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Serialize, Deserialize)]
pub enum NodeType {
    Empty,    // 完全空旷
    Blocked,  // 完全被障碍物占据
    Mixed,    // 包含障碍物和空旷区域
}

/// 2D点结构
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, Serialize, Deserialize)]
pub struct Point {
    pub x: f32,
    pub y: f32,
}

impl Point {
    pub fn new(x: f32, y: f32) -> Self {
        Self { x, y }
    }

    pub fn distance_to(&self, other: &Point) -> f32 {
        let dx = self.x - other.x;
        let dy = self.y - other.y;
        (dx * dx + dy * dy).sqrt()
    }
}

/// 3D点结构
#[derive(Debu<PERSON>, <PERSON><PERSON>, Copy, Serialize, Deserialize)]
pub struct Point3D {
    pub x: f32,
    pub y: f32,
    pub z: f32,
}

impl Point3D {
    pub fn new(x: f32, y: f32, z: f32) -> Self {
        Self { x, y, z }
    }

    pub fn distance_to(&self, other: &Point3D) -> f32 {
        let dx = self.x - other.x;
        let dy = self.y - other.y;
        let dz = self.z - other.z;
        (dx * dx + dy * dy + dz * dz).sqrt()
    }
}

/// 2D矩形边界
#[derive(Debug, Clone, Copy)]
pub struct Rectangle {
    pub x: f32,
    pub y: f32,
    pub width: f32,
    pub height: f32,
}

/// 3D长方体边界
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub struct Box3D {
    pub x: f32,
    pub y: f32,
    pub z: f32,
    pub width: f32,
    pub height: f32,
    pub depth: f32,
}

/// 2D障碍物类型枚举
#[derive(Debug, Clone)]
pub enum Obstacle {
    Rectangle(Rectangle),
    Circle { center: Point, radius: f32 },
    Polygon { vertices: Vec<Point> },
    PointSet { points: Vec<Point>, radius: f32 }, // 点集，每个点有一个影响半径
}

/// 3D障碍物类型枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Obstacle3D {
    Box(Box3D),
    Cylinder { center: Point3D, radius: f32, height: f32 },
    Prism { vertices: Vec<Point>, bottom: f32, height: f32 }, // 棱柱：底面多边形 + 高度
    PointSet { points: Vec<Point3D>, radius: f32 }, // 3D点集，每个点有一个影响半径
}

impl Rectangle {
    pub fn new(x: f32, y: f32, width: f32, height: f32) -> Self {
        Self { x, y, width, height }
    }

    pub fn contains_point(&self, px: f32, py: f32) -> bool {
        px >= self.x && px < self.x + self.width &&
        py >= self.y && py < self.y + self.height
    }

    pub fn center(&self) -> (f32, f32) {
        (self.x + self.width / 2.0, self.y + self.height / 2.0)
    }
}

impl Box3D {
    pub fn new(x: f32, y: f32, z: f32, width: f32, height: f32, depth: f32) -> Self {
        Self { x, y, z, width, height, depth }
    }

    pub fn contains_point(&self, px: f32, py: f32, pz: f32) -> bool {
        px >= self.x && px < self.x + self.width &&
        py >= self.y && py < self.y + self.height &&
        pz >= self.z && pz < self.z + self.depth
    }

    pub fn center(&self) -> (f32, f32, f32) {
        (
            self.x + self.width / 2.0,
            self.y + self.height / 2.0,
            self.z + self.depth / 2.0
        )
    }
}

impl Obstacle3D {
    /// 检查3D点是否在障碍物内
    pub fn contains_point(&self, x: f32, y: f32, z: f32) -> bool {
        match self {
            Obstacle3D::Box(box3d) => box3d.contains_point(x, y, z),
            Obstacle3D::Cylinder { center, radius, height } => {
                // 检查高度范围
                if z < center.z || z > center.z + height {
                    return false;
                }
                // 检查2D圆形范围
                let dx = x - center.x;
                let dy = y - center.y;
                dx * dx + dy * dy <= radius * radius
            }
            Obstacle3D::Prism { vertices, bottom, height } => {
                // 检查高度范围
                if z < *bottom || z > bottom + height {
                    return false;
                }
                // 使用2D多边形检测
                Self::point_in_polygon(x, y, vertices)
            }
            Obstacle3D::PointSet { points, radius } => {
                points.iter().any(|point| {
                    let dx = x - point.x;
                    let dy = y - point.y;
                    let dz = z - point.z;
                    dx * dx + dy * dy + dz * dz <= radius * radius
                })
            }
        }
    }

    /// 检查障碍物是否与3D长方体区域有重叠
    pub fn overlaps_with_box(&self, box3d: &Box3D) -> bool {
        match self {
            Obstacle3D::Box(obs_box) => {
                Self::box_overlaps(box3d, obs_box)
            }
            Obstacle3D::Cylinder { center, radius, height } => {
                Self::cylinder_overlaps_box(center, *radius, *height, box3d)
            }
            Obstacle3D::Prism { vertices, bottom, height } => {
                Self::prism_overlaps_box(vertices, *bottom, *height, box3d)
            }
            Obstacle3D::PointSet { points, radius } => {
                Self::pointset_overlaps_box(points, *radius, box3d)
            }
        }
    }

    /// 计算障碍物与3D长方体区域的重叠体积
    pub fn overlap_with_box(&self, box3d: &Box3D) -> f32 {
        match self {
            Obstacle3D::Box(obs_box) => {
                Self::box_overlap_volume(box3d, obs_box)
            }
            Obstacle3D::Cylinder { center, radius, height } => {
                Self::cylinder_box_overlap_volume(center, *radius, *height, box3d)
            }
            Obstacle3D::Prism { vertices, bottom, height } => {
                Self::prism_box_overlap_volume(vertices, *bottom, *height, box3d)
            }
            Obstacle3D::PointSet { points, radius } => {
                Self::pointset_box_overlap_volume(points, *radius, box3d)
            }
        }
    }

    /// 检查两个3D长方体是否重叠
    fn box_overlaps(box1: &Box3D, box2: &Box3D) -> bool {
        !(box1.x + box1.width <= box2.x ||
          box2.x + box2.width <= box1.x ||
          box1.y + box1.height <= box2.y ||
          box2.y + box2.height <= box1.y ||
          box1.z + box1.depth <= box2.z ||
          box2.z + box2.depth <= box1.z)
    }

    /// 检查圆柱体是否与3D长方体重叠
    fn cylinder_overlaps_box(center: &Point3D, radius: f32, height: f32, box3d: &Box3D) -> bool {
        // 检查高度范围是否重叠
        if center.z + height <= box3d.z || box3d.z + box3d.depth <= center.z {
            return false;
        }

        // 检查2D圆形与矩形是否重叠
        let closest_x = center.x.max(box3d.x).min(box3d.x + box3d.width);
        let closest_y = center.y.max(box3d.y).min(box3d.y + box3d.height);

        let dx = center.x - closest_x;
        let dy = center.y - closest_y;
        let distance_squared = dx * dx + dy * dy;

        distance_squared <= radius * radius
    }

    /// 检查棱柱是否与3D长方体重叠
    fn prism_overlaps_box(vertices: &[Point], bottom: f32, height: f32, box3d: &Box3D) -> bool {
        // 检查高度范围是否重叠
        if bottom + height <= box3d.z || box3d.z + box3d.depth <= bottom {
            return false;
        }

        // 创建2D矩形用于检测
        let rect = Rectangle::new(box3d.x, box3d.y, box3d.width, box3d.height);

        // 复用2D多边形与矩形重叠检测
        Obstacle::polygon_overlaps_rectangle(vertices, &rect)
    }

    /// 检查3D点集是否与长方体重叠
    fn pointset_overlaps_box(points: &[Point3D], radius: f32, box3d: &Box3D) -> bool {
        for point in points {
            if Self::sphere_overlaps_box(point, radius, box3d) {
                return true;
            }
        }
        false
    }

    /// 检查球体是否与长方体重叠
    fn sphere_overlaps_box(center: &Point3D, radius: f32, box3d: &Box3D) -> bool {
        let closest_x = center.x.max(box3d.x).min(box3d.x + box3d.width);
        let closest_y = center.y.max(box3d.y).min(box3d.y + box3d.height);
        let closest_z = center.z.max(box3d.z).min(box3d.z + box3d.depth);

        let dx = center.x - closest_x;
        let dy = center.y - closest_y;
        let dz = center.z - closest_z;
        let distance_squared = dx * dx + dy * dy + dz * dz;

        distance_squared <= radius * radius
    }

    /// 复用2D的点在多边形内检测
    fn point_in_polygon(x: f32, y: f32, vertices: &[Point]) -> bool {
        Obstacle::point_in_polygon(x, y, vertices)
    }

    /// 3D长方体重叠体积计算
    fn box_overlap_volume(box1: &Box3D, box2: &Box3D) -> f32 {
        let x1 = box1.x.max(box2.x);
        let y1 = box1.y.max(box2.y);
        let z1 = box1.z.max(box2.z);
        let x2 = (box1.x + box1.width).min(box2.x + box2.width);
        let y2 = (box1.y + box1.height).min(box2.y + box2.height);
        let z2 = (box1.z + box1.depth).min(box2.z + box2.depth);

        if x2 > x1 && y2 > y1 && z2 > z1 {
            (x2 - x1) * (y2 - y1) * (z2 - z1)
        } else {
            0.0
        }
    }

    /// 圆柱体与长方体重叠体积（近似计算）
    fn cylinder_box_overlap_volume(center: &Point3D, radius: f32, height: f32, box3d: &Box3D) -> f32 {
        // 简化计算：采样检测
        let samples = 10;
        let step_x = box3d.width / samples as f32;
        let step_y = box3d.height / samples as f32;
        let step_z = box3d.depth / samples as f32;
        let sample_volume = step_x * step_y * step_z;

        let mut overlap_samples = 0;
        for i in 0..samples {
            for j in 0..samples {
                for k in 0..samples {
                    let x = box3d.x + (i as f32 + 0.5) * step_x;
                    let y = box3d.y + (j as f32 + 0.5) * step_y;
                    let z = box3d.z + (k as f32 + 0.5) * step_z;

                    // 检查是否在圆柱体内
                    if z >= center.z && z <= center.z + height {
                        let dx = x - center.x;
                        let dy = y - center.y;
                        if dx * dx + dy * dy <= radius * radius {
                            overlap_samples += 1;
                        }
                    }
                }
            }
        }

        overlap_samples as f32 * sample_volume
    }

    /// 棱柱与长方体重叠体积（近似计算）
    fn prism_box_overlap_volume(vertices: &[Point], bottom: f32, height: f32, box3d: &Box3D) -> f32 {
        // 简化计算：采样检测
        let samples = 10;
        let step_x = box3d.width / samples as f32;
        let step_y = box3d.height / samples as f32;
        let step_z = box3d.depth / samples as f32;
        let sample_volume = step_x * step_y * step_z;

        let mut overlap_samples = 0;
        for i in 0..samples {
            for j in 0..samples {
                for k in 0..samples {
                    let x = box3d.x + (i as f32 + 0.5) * step_x;
                    let y = box3d.y + (j as f32 + 0.5) * step_y;
                    let z = box3d.z + (k as f32 + 0.5) * step_z;

                    // 检查是否在棱柱内
                    if z >= bottom && z <= bottom + height {
                        if Self::point_in_polygon(x, y, vertices) {
                            overlap_samples += 1;
                        }
                    }
                }
            }
        }

        overlap_samples as f32 * sample_volume
    }

    /// 3D点集与长方体重叠体积
    fn pointset_box_overlap_volume(points: &[Point3D], radius: f32, box3d: &Box3D) -> f32 {
        let mut total_overlap = 0.0;
        for point in points {
            total_overlap += Self::sphere_box_overlap_volume(point, radius, box3d);
        }
        total_overlap
    }

    /// 球体与长方体重叠体积（近似计算）
    fn sphere_box_overlap_volume(center: &Point3D, radius: f32, box3d: &Box3D) -> f32 {
        // 简化计算：采样检测
        let samples = 10;
        let step_x = box3d.width / samples as f32;
        let step_y = box3d.height / samples as f32;
        let step_z = box3d.depth / samples as f32;
        let sample_volume = step_x * step_y * step_z;

        let mut overlap_samples = 0;
        for i in 0..samples {
            for j in 0..samples {
                for k in 0..samples {
                    let x = box3d.x + (i as f32 + 0.5) * step_x;
                    let y = box3d.y + (j as f32 + 0.5) * step_y;
                    let z = box3d.z + (k as f32 + 0.5) * step_z;

                    let dx = x - center.x;
                    let dy = y - center.y;
                    let dz = z - center.z;
                    if dx * dx + dy * dy + dz * dz <= radius * radius {
                        overlap_samples += 1;
                    }
                }
            }
        }

        overlap_samples as f32 * sample_volume
    }
}

impl Obstacle {
    /// 检查点是否在障碍物内
    pub fn contains_point(&self, x: f32, y: f32) -> bool {
        match self {
            Obstacle::Rectangle(rect) => rect.contains_point(x, y),
            Obstacle::Circle { center, radius } => {
                let dx = x - center.x;
                let dy = y - center.y;
                dx * dx + dy * dy <= radius * radius
            }
            Obstacle::Polygon { vertices } => {
                Self::point_in_polygon(x, y, vertices)
            }
            Obstacle::PointSet { points, radius } => {
                points.iter().any(|point| {
                    let dx = x - point.x;
                    let dy = y - point.y;
                    dx * dx + dy * dy <= radius * radius
                })
            }
        }
    }

    /// 检查障碍物是否与矩形区域有重叠（用于网格分类）
    /// 对于路径规划，只要有任何重叠就认为整个网格不可通行
    pub fn overlaps_with_rectangle(&self, rect: &Rectangle) -> bool {
        match self {
            Obstacle::Rectangle(obs_rect) => {
                Self::rectangle_overlaps(rect, obs_rect)
            }
            Obstacle::Circle { center, radius } => {
                Self::circle_overlaps_rectangle(center, *radius, rect)
            }
            Obstacle::Polygon { vertices } => {
                Self::polygon_overlaps_rectangle(vertices, rect)
            }
            Obstacle::PointSet { points, radius } => {
                Self::pointset_overlaps_rectangle(points, *radius, rect)
            }
        }
    }

    /// 计算障碍物与矩形区域的重叠面积（用于可视化和统计）
    pub fn overlap_with_rectangle(&self, rect: &Rectangle) -> f32 {
        match self {
            Obstacle::Rectangle(obs_rect) => {
                Self::rectangle_overlap(rect, obs_rect)
            }
            Obstacle::Circle { center, radius } => {
                Self::circle_rectangle_overlap(center, *radius, rect)
            }
            Obstacle::Polygon { vertices } => {
                Self::polygon_rectangle_overlap(vertices, rect)
            }
            Obstacle::PointSet { points, radius } => {
                Self::pointset_rectangle_overlap(points, *radius, rect)
            }
        }
    }

    /// 检查两个矩形是否重叠
    fn rectangle_overlaps(rect1: &Rectangle, rect2: &Rectangle) -> bool {
        !(rect1.x + rect1.width <= rect2.x ||
          rect2.x + rect2.width <= rect1.x ||
          rect1.y + rect1.height <= rect2.y ||
          rect2.y + rect2.height <= rect1.y)
    }

    /// 检查圆形是否与矩形重叠
    fn circle_overlaps_rectangle(center: &Point, radius: f32, rect: &Rectangle) -> bool {
        // 找到矩形上距离圆心最近的点
        let closest_x = center.x.max(rect.x).min(rect.x + rect.width);
        let closest_y = center.y.max(rect.y).min(rect.y + rect.height);

        // 计算距离
        let dx = center.x - closest_x;
        let dy = center.y - closest_y;
        let distance_squared = dx * dx + dy * dy;

        distance_squared <= radius * radius
    }

    /// 检查多边形是否与矩形重叠
    fn polygon_overlaps_rectangle(vertices: &[Point], rect: &Rectangle) -> bool {
        if vertices.len() < 3 {
            return false;
        }

        // 检查多边形顶点是否在矩形内
        for vertex in vertices {
            if rect.contains_point(vertex.x, vertex.y) {
                return true;
            }
        }

        // 检查矩形顶点是否在多边形内
        let rect_corners = [
            (rect.x, rect.y),
            (rect.x + rect.width, rect.y),
            (rect.x + rect.width, rect.y + rect.height),
            (rect.x, rect.y + rect.height),
        ];

        for &(x, y) in &rect_corners {
            if Self::point_in_polygon(x, y, vertices) {
                return true;
            }
        }

        // 检查多边形边是否与矩形边相交
        for i in 0..vertices.len() {
            let j = (i + 1) % vertices.len();
            let line_start = &vertices[i];
            let line_end = &vertices[j];

            // 检查与矩形四条边的相交
            if Self::line_intersects_rectangle(line_start, line_end, rect) {
                return true;
            }
        }

        false
    }

    /// 检查点集是否与矩形重叠
    fn pointset_overlaps_rectangle(points: &[Point], radius: f32, rect: &Rectangle) -> bool {
        for point in points {
            if Self::circle_overlaps_rectangle(point, radius, rect) {
                return true;
            }
        }
        false
    }

    /// 检查线段是否与矩形相交
    fn line_intersects_rectangle(line_start: &Point, line_end: &Point, rect: &Rectangle) -> bool {
        // 简化版本：检查线段是否与矩形的任一边相交
        let rect_lines = [
            (Point::new(rect.x, rect.y), Point::new(rect.x + rect.width, rect.y)),
            (Point::new(rect.x + rect.width, rect.y), Point::new(rect.x + rect.width, rect.y + rect.height)),
            (Point::new(rect.x + rect.width, rect.y + rect.height), Point::new(rect.x, rect.y + rect.height)),
            (Point::new(rect.x, rect.y + rect.height), Point::new(rect.x, rect.y)),
        ];

        for (rect_start, rect_end) in &rect_lines {
            if Self::lines_intersect(line_start, line_end, rect_start, rect_end) {
                return true;
            }
        }

        false
    }

    /// 检查两条线段是否相交
    fn lines_intersect(p1: &Point, q1: &Point, p2: &Point, q2: &Point) -> bool {
        fn orientation(p: &Point, q: &Point, r: &Point) -> i32 {
            let val = (q.y - p.y) * (r.x - q.x) - (q.x - p.x) * (r.y - q.y);
            if val.abs() < 1e-10 { 0 } // 共线
            else if val > 0.0 { 1 } // 顺时针
            else { 2 } // 逆时针
        }

        fn on_segment(p: &Point, q: &Point, r: &Point) -> bool {
            q.x <= p.x.max(r.x) && q.x >= p.x.min(r.x) &&
            q.y <= p.y.max(r.y) && q.y >= p.y.min(r.y)
        }

        let o1 = orientation(p1, q1, p2);
        let o2 = orientation(p1, q1, q2);
        let o3 = orientation(p2, q2, p1);
        let o4 = orientation(p2, q2, q1);

        // 一般情况
        if o1 != o2 && o3 != o4 {
            return true;
        }

        // 特殊情况：共线且重叠
        (o1 == 0 && on_segment(p1, p2, q1)) ||
        (o2 == 0 && on_segment(p1, q2, q1)) ||
        (o3 == 0 && on_segment(p2, p1, q2)) ||
        (o4 == 0 && on_segment(p2, q1, q2))
    }

    /// 矩形与矩形重叠面积（保留用于统计）
    fn rectangle_overlap(rect1: &Rectangle, rect2: &Rectangle) -> f32 {
        let x1 = rect1.x.max(rect2.x);
        let y1 = rect1.y.max(rect2.y);
        let x2 = (rect1.x + rect1.width).min(rect2.x + rect2.width);
        let y2 = (rect1.y + rect1.height).min(rect2.y + rect2.height);

        if x2 > x1 && y2 > y1 {
            (x2 - x1) * (y2 - y1)
        } else {
            0.0
        }
    }

    /// 圆形与矩形重叠面积（近似计算）
    fn circle_rectangle_overlap(center: &Point, radius: f32, rect: &Rectangle) -> f32 {
        // 简化计算：采样检测
        let samples = 20;
        let step_x = rect.width / samples as f32;
        let step_y = rect.height / samples as f32;
        let sample_area = step_x * step_y;

        let mut overlap_samples = 0;
        for i in 0..samples {
            for j in 0..samples {
                let x = rect.x + (i as f32 + 0.5) * step_x;
                let y = rect.y + (j as f32 + 0.5) * step_y;

                let dx = x - center.x;
                let dy = y - center.y;
                if dx * dx + dy * dy <= radius * radius {
                    overlap_samples += 1;
                }
            }
        }

        overlap_samples as f32 * sample_area
    }

    /// 多边形与矩形重叠面积（近似计算）
    fn polygon_rectangle_overlap(vertices: &[Point], rect: &Rectangle) -> f32 {
        // 简化计算：采样检测
        let samples = 20;
        let step_x = rect.width / samples as f32;
        let step_y = rect.height / samples as f32;
        let sample_area = step_x * step_y;

        let mut overlap_samples = 0;
        for i in 0..samples {
            for j in 0..samples {
                let x = rect.x + (i as f32 + 0.5) * step_x;
                let y = rect.y + (j as f32 + 0.5) * step_y;

                if Self::point_in_polygon(x, y, vertices) {
                    overlap_samples += 1;
                }
            }
        }

        overlap_samples as f32 * sample_area
    }

    /// 点集与矩形重叠面积
    fn pointset_rectangle_overlap(points: &[Point], radius: f32, rect: &Rectangle) -> f32 {
        let mut total_overlap = 0.0;
        for point in points {
            total_overlap += Self::circle_rectangle_overlap(point, radius, rect);
        }
        total_overlap
    }

    /// 点在多边形内检测（射线法）
    fn point_in_polygon(x: f32, y: f32, vertices: &[Point]) -> bool {
        if vertices.len() < 3 {
            return false;
        }

        let mut inside = false;
        let mut j = vertices.len() - 1;

        for i in 0..vertices.len() {
            let xi = vertices[i].x;
            let yi = vertices[i].y;
            let xj = vertices[j].x;
            let yj = vertices[j].y;

            if ((yi > y) != (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi) {
                inside = !inside;
            }
            j = i;
        }

        inside
    }
}

/// 四叉树节点
#[derive(Debug)]
pub struct QuadTreeNode {
    pub bounds: Rectangle,
    pub level: u8,
    pub node_type: NodeType,
    pub children: Option<Box<[QuadTreeNode; 4]>>,
}

/// 八叉树节点
#[derive(Debug)]
pub struct OctreeNode {
    pub bounds: Box3D,
    pub level: u8,
    pub node_type: NodeType,
    pub children: Option<Box<[OctreeNode; 8]>>,
}

impl QuadTreeNode {
    pub fn new(bounds: Rectangle, level: u8) -> Self {
        Self {
            bounds,
            level,
            node_type: NodeType::Empty,
            children: None,
        }
    }

    pub fn is_leaf(&self) -> bool {
        self.children.is_none()
    }

    /// 获取所有叶子节点
    pub fn get_leaf_nodes(&self) -> Vec<&QuadTreeNode> {
        if self.is_leaf() {
            vec![self]
        } else {
            let mut leaves = Vec::new();
            if let Some(children) = &self.children {
                for child in children.iter() {
                    leaves.extend(child.get_leaf_nodes());
                }
            }
            leaves
        }
    }
}

impl OctreeNode {
    pub fn new(bounds: Box3D, level: u8) -> Self {
        Self {
            bounds,
            level,
            node_type: NodeType::Empty,
            children: None,
        }
    }

    pub fn is_leaf(&self) -> bool {
        self.children.is_none()
    }

    /// 获取所有叶子节点
    pub fn get_leaf_nodes(&self) -> Vec<&OctreeNode> {
        if self.is_leaf() {
            vec![self]
        } else {
            let mut leaves = Vec::new();
            if let Some(children) = &self.children {
                for child in children.iter() {
                    leaves.extend(child.get_leaf_nodes());
                }
            }
            leaves
        }
    }
}

/// 2D网格单元
#[derive(Debug, Clone)]
pub struct GridCell {
    pub id: usize,
    pub bounds: Rectangle,
    pub level: u8,
    pub node_type: NodeType,
}

/// 3D网格单元
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GridCell3D {
    pub id: usize,
    pub bounds: Box3D,
    pub level: u8,
    pub node_type: NodeType,
}

/// 3D网格边界信息（用于可视化）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GridBoundary3D {
    pub x: f32,
    pub y: f32,
    pub z: f32,
    pub width: f32,
    pub height: f32,
    pub depth: f32,
    pub level: u8,
    pub node_type: NodeType,
}

/// 3D体素数据（用于Python可视化）
#[derive(Debug, Serialize, Deserialize)]
pub struct VoxelData3D {
    pub size: u32,  // 立方体边长
    pub voxels: Vec<u8>,  // 0=空, 1=空旷网格, 2=被占据网格, 3=障碍物
    pub grid_boundaries: Vec<GridBoundary3D>,
    pub path_points: Option<Vec<Point3D>>,  // 路径点
}

/// 路径规划中的节点
#[derive(Debug, Clone)]
pub struct PathNode {
    pub cell_id: usize,
    pub g_cost: f32,
    pub h_cost: f32,
    pub parent: Option<usize>,
}

impl PathNode {
    pub fn f_cost(&self) -> f32 {
        self.g_cost + self.h_cost
    }
}

impl Eq for PathNode {}

impl PartialEq for PathNode {
    fn eq(&self, other: &Self) -> bool {
        self.cell_id == other.cell_id
    }
}

impl Ord for PathNode {
    fn cmp(&self, other: &Self) -> Ordering {
        // 注意：BinaryHeap是最大堆，我们需要最小堆，所以反转比较
        other.f_cost().partial_cmp(&self.f_cost()).unwrap_or(Ordering::Equal)
    }
}

impl PartialOrd for PathNode {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some(self.cmp(other))
    }
}

/// 2D多分辨率网格系统 - 基于规则网格而非四叉树
pub struct MultiResolutionGrid {
    pub cells: Vec<GridCell>,
    pub map_size: u32,  // 改为整数，确保对齐
    pub obstacles: Vec<Obstacle>,
    pub adjacency: HashMap<usize, Vec<(usize, f32)>>, // 邻接关系：(邻居ID, 边代价)
}

/// 3D多分辨率网格系统 - 基于八叉树规则网格
pub struct MultiResolutionGrid3D {
    pub cells: Vec<GridCell3D>,
    pub map_size: u32,  // 立方体边长，确保是2的幂次
    pub obstacles: Vec<Obstacle3D>,
    pub adjacency: HashMap<usize, Vec<(usize, f32)>>, // 邻接关系：(邻居ID, 边代价)
}

impl MultiResolutionGrid3D {
    pub fn new(map_size: u32) -> Self {
        Self {
            cells: Vec::new(),
            map_size,
            obstacles: Vec::new(),
            adjacency: HashMap::new(),
        }
    }

    /// 添加3D障碍物
    pub fn add_obstacle(&mut self, obstacle: Obstacle3D) {
        self.obstacles.push(obstacle);
    }

    /// 检查3D区域是否包含障碍物
    fn check_obstacle_in_region(region: &Box3D, obstacles: &[Obstacle3D]) -> NodeType {
        // 对于1x1x1的最小网格，采用保守策略：只要有重叠就不可通行
        if region.width <= 1.0 && region.height <= 1.0 && region.depth <= 1.0 {
            for obstacle in obstacles {
                if obstacle.overlaps_with_box(region) {
                    return NodeType::Blocked;
                }
            }
            return NodeType::Empty;
        }

        // 对于较大的网格，检查重叠情况来决定是否需要细分
        let mut has_overlap = false;
        let region_volume = region.width * region.height * region.depth;
        let mut total_overlap_volume = 0.0;

        for obstacle in obstacles {
            if obstacle.overlaps_with_box(region) {
                has_overlap = true;
                let overlap_volume = obstacle.overlap_with_box(region);
                total_overlap_volume += overlap_volume;
            }
        }

        if !has_overlap {
            NodeType::Empty
        } else if total_overlap_volume >= region_volume * 0.99 {
            NodeType::Blocked  // 几乎完全被覆盖
        } else {
            NodeType::Mixed    // 部分覆盖，需要细分
        }
    }

    /// 构建3D自适应网格 - 使用分层规则网格
    pub fn build_adaptive_grid(&mut self) {
        self.cells.clear();
        self.adjacency.clear();

        // 从最粗的网格开始，递归细化需要细化的区域
        let max_level = (self.map_size as f32).log2() as u8;

        // 从整个3D地图开始递归细化
        let root_bounds = Box3D::new(0.0, 0.0, 0.0,
                                     self.map_size as f32,
                                     self.map_size as f32,
                                     self.map_size as f32);
        self.subdivide_region(root_bounds, 0, max_level);

        // 为每个单元分配ID
        for (i, cell) in self.cells.iter_mut().enumerate() {
            cell.id = i;
        }

        // 构建邻接关系
        self.build_adjacency();
    }

    /// 递归细分3D区域 - 确保网格边界对齐
    fn subdivide_region(&mut self, bounds: Box3D, level: u8, max_level: u8) {
        // 检查当前区域的类型
        let node_type = Self::check_obstacle_in_region(&bounds, &self.obstacles);

        // 如果是纯空旷或纯障碍区域，或者已达到最大深度，或者已经是1x1x1大小，则停止细分
        if node_type != NodeType::Mixed || level >= max_level ||
           bounds.width <= 1.0 || bounds.height <= 1.0 || bounds.depth <= 1.0 {
            let cell = GridCell3D {
                id: 0, // 临时ID，稍后会重新分配
                bounds,
                level,
                node_type,
            };
            self.cells.push(cell);
            return;
        }

        // 如果是混合区域，则细分为8个子区域
        let current_size = bounds.width as u32;
        let half_size = current_size / 2;

        // 只有当当前大小是偶数时才能均匀细分
        if current_size % 2 != 0 || half_size == 0 {
            let cell = GridCell3D {
                id: 0, // 临时ID，稍后会重新分配
                bounds,
                level,
                node_type,
            };
            self.cells.push(cell);
            return;
        }

        let x = bounds.x as u32;
        let y = bounds.y as u32;
        let z = bounds.z as u32;

        // 8个子立方体
        let children = [
            Box3D::new(x as f32, y as f32, z as f32, half_size as f32, half_size as f32, half_size as f32),
            Box3D::new((x + half_size) as f32, y as f32, z as f32, half_size as f32, half_size as f32, half_size as f32),
            Box3D::new(x as f32, (y + half_size) as f32, z as f32, half_size as f32, half_size as f32, half_size as f32),
            Box3D::new((x + half_size) as f32, (y + half_size) as f32, z as f32, half_size as f32, half_size as f32, half_size as f32),
            Box3D::new(x as f32, y as f32, (z + half_size) as f32, half_size as f32, half_size as f32, half_size as f32),
            Box3D::new((x + half_size) as f32, y as f32, (z + half_size) as f32, half_size as f32, half_size as f32, half_size as f32),
            Box3D::new(x as f32, (y + half_size) as f32, (z + half_size) as f32, half_size as f32, half_size as f32, half_size as f32),
            Box3D::new((x + half_size) as f32, (y + half_size) as f32, (z + half_size) as f32, half_size as f32, half_size as f32, half_size as f32),
        ];

        // 递归处理每个子区域
        for child_bounds in children.iter() {
            self.subdivide_region(*child_bounds, level + 1, max_level);
        }
    }

    /// 构建3D邻接关系
    fn build_adjacency(&mut self) {
        self.adjacency.clear();

        for i in 0..self.cells.len() {
            let mut neighbors = Vec::new();

            for j in 0..self.cells.len() {
                if i != j && self.are_adjacent(i, j) {
                    let cost = self.calculate_edge_cost(i, j);
                    neighbors.push((j, cost));
                }
            }

            self.adjacency.insert(i, neighbors);
        }
    }

    /// 检查两个3D网格单元是否相邻
    fn are_adjacent(&self, cell_a_id: usize, cell_b_id: usize) -> bool {
        let cell_a = &self.cells[cell_a_id];
        let cell_b = &self.cells[cell_b_id];

        // 只有空旷单元才能通行
        if cell_a.node_type == NodeType::Blocked || cell_b.node_type == NodeType::Blocked {
            return false;
        }

        // 检查两个3D长方体是否共享面
        let a = &cell_a.bounds;
        let b = &cell_b.bounds;

        // 检查X方向相邻（YZ面共享）
        let x_adjacent = (a.x + a.width == b.x || b.x + b.width == a.x) &&
                        !(a.y + a.height <= b.y || b.y + b.height <= a.y) &&
                        !(a.z + a.depth <= b.z || b.z + b.depth <= a.z);

        // 检查Y方向相邻（XZ面共享）
        let y_adjacent = (a.y + a.height == b.y || b.y + b.height == a.y) &&
                        !(a.x + a.width <= b.x || b.x + b.width <= a.x) &&
                        !(a.z + a.depth <= b.z || b.z + b.depth <= a.z);

        // 检查Z方向相邻（XY面共享）
        let z_adjacent = (a.z + a.depth == b.z || b.z + b.depth == a.z) &&
                        !(a.x + a.width <= b.x || b.x + b.width <= a.x) &&
                        !(a.y + a.height <= b.y || b.y + b.height <= a.y);

        x_adjacent || y_adjacent || z_adjacent
    }

    /// 计算两个相邻3D网格单元之间的边代价
    fn calculate_edge_cost(&self, cell_a_id: usize, cell_b_id: usize) -> f32 {
        let cell_a = &self.cells[cell_a_id];
        let cell_b = &self.cells[cell_b_id];

        let center_a = cell_a.bounds.center();
        let center_b = cell_b.bounds.center();

        // 使用3D欧几里得距离
        let dx = center_a.0 - center_b.0;
        let dy = center_a.1 - center_b.1;
        let dz = center_a.2 - center_b.2;
        (dx * dx + dy * dy + dz * dz).sqrt()
    }

    /// 找到包含指定3D点的网格单元
    pub fn find_cell_containing_point(&self, x: f32, y: f32, z: f32) -> Option<usize> {
        for (i, cell) in self.cells.iter().enumerate() {
            if cell.bounds.contains_point(x, y, z) && cell.node_type != NodeType::Blocked {
                return Some(i);
            }
        }
        None
    }

    /// 3D A*路径规划算法
    pub fn find_path(&self, start_x: f32, start_y: f32, start_z: f32,
                     goal_x: f32, goal_y: f32, goal_z: f32) -> Option<Vec<usize>> {
        // 找到起点和终点所在的网格单元
        let start_cell = self.find_cell_containing_point(start_x, start_y, start_z)?;
        let goal_cell = self.find_cell_containing_point(goal_x, goal_y, goal_z)?;

        if start_cell == goal_cell {
            return Some(vec![start_cell]);
        }

        let mut open_set = BinaryHeap::new();
        let mut closed_set = HashSet::new();
        let mut came_from: HashMap<usize, usize> = HashMap::new();
        let mut g_score: HashMap<usize, f32> = HashMap::new();

        // 初始化起点
        g_score.insert(start_cell, 0.0);
        let h_cost = self.heuristic(start_cell, goal_cell);
        open_set.push(PathNode {
            cell_id: start_cell,
            g_cost: 0.0,
            h_cost,
            parent: None,
        });

        while let Some(current_node) = open_set.pop() {
            let current_cell = current_node.cell_id;

            if current_cell == goal_cell {
                // 重建路径
                return Some(self.reconstruct_path(&came_from, current_cell));
            }

            closed_set.insert(current_cell);

            // 检查所有邻居
            if let Some(neighbors) = self.adjacency.get(&current_cell) {
                for &(neighbor_cell, edge_cost) in neighbors {
                    if closed_set.contains(&neighbor_cell) {
                        continue;
                    }

                    let tentative_g = current_node.g_cost + edge_cost;

                    let is_better = if let Some(&existing_g) = g_score.get(&neighbor_cell) {
                        tentative_g < existing_g
                    } else {
                        true
                    };

                    if is_better {
                        came_from.insert(neighbor_cell, current_cell);
                        g_score.insert(neighbor_cell, tentative_g);

                        let h_cost = self.heuristic(neighbor_cell, goal_cell);
                        open_set.push(PathNode {
                            cell_id: neighbor_cell,
                            g_cost: tentative_g,
                            h_cost,
                            parent: Some(current_cell),
                        });
                    }
                }
            }
        }

        None // 没有找到路径
    }

    /// 3D启发式函数（欧几里得距离）
    fn heuristic(&self, cell_a: usize, cell_b: usize) -> f32 {
        let center_a = self.cells[cell_a].bounds.center();
        let center_b = self.cells[cell_b].bounds.center();

        let dx = center_a.0 - center_b.0;
        let dy = center_a.1 - center_b.1;
        let dz = center_a.2 - center_b.2;
        (dx * dx + dy * dy + dz * dz).sqrt()
    }

    /// 重建路径
    fn reconstruct_path(&self, came_from: &HashMap<usize, usize>, mut current: usize) -> Vec<usize> {
        let mut path = vec![current];

        while let Some(&parent) = came_from.get(&current) {
            current = parent;
            path.push(current);
        }

        path.reverse();
        path
    }

    /// 导出体素数据用于Python可视化
    pub fn export_voxel_data(&self) -> VoxelData3D {
        let size = self.map_size;
        let mut voxels = vec![0u8; (size * size * size) as usize];

        // 直接根据网格单元类型来标记体素，确保一致性
        for cell in &self.cells {
            let bounds = &cell.bounds;
            let start_x = bounds.x as u32;
            let start_y = bounds.y as u32;
            let start_z = bounds.z as u32;
            let end_x = (bounds.x + bounds.width) as u32;
            let end_y = (bounds.y + bounds.height) as u32;
            let end_z = (bounds.z + bounds.depth) as u32;

            let voxel_value = match cell.node_type {
                NodeType::Empty => 1,     // 绿色边框 - 空旷网格
                NodeType::Blocked => 3,   // 红色边框 + 黑色填充 - 被占据网格
                NodeType::Mixed => {
                    // 理论上不应该有Mixed类型的叶子节点
                    eprintln!("警告: 发现Mixed类型的网格单元，这不应该发生！");
                    1  // 当作空旷处理
                }
            };

            // 填充整个网格单元
            for x in start_x..end_x {
                for y in start_y..end_y {
                    for z in start_z..end_z {
                        if x < size && y < size && z < size {
                            let index = (z * size * size + y * size + x) as usize;
                            voxels[index] = voxel_value;
                        }
                    }
                }
            }
        }

        // 收集网格边界信息
        let grid_boundaries: Vec<GridBoundary3D> = self.cells.iter().map(|cell| {
            GridBoundary3D {
                x: cell.bounds.x,
                y: cell.bounds.y,
                z: cell.bounds.z,
                width: cell.bounds.width,
                height: cell.bounds.height,
                depth: cell.bounds.depth,
                level: cell.level,
                node_type: cell.node_type,
            }
        }).collect();

        VoxelData3D {
            size,
            voxels,
            grid_boundaries,
            path_points: None,  // 暂时为空，稍后添加路径数据
        }
    }

    /// 保存体素数据到JSON文件
    pub fn save_voxel_data_to_file(&self, filename: &str) -> Result<(), Box<dyn std::error::Error>> {
        let voxel_data = self.export_voxel_data();
        let json_string = serde_json::to_string_pretty(&voxel_data)?;
        std::fs::write(filename, json_string)?;
        Ok(())
    }
}

impl MultiResolutionGrid {
    pub fn new(map_size: u32) -> Self {
        Self {
            cells: Vec::new(),
            map_size,
            obstacles: Vec::new(),
            adjacency: HashMap::new(),
        }
    }

    /// 添加矩形障碍物（兼容性方法）
    pub fn add_obstacle(&mut self, obstacle: Rectangle) {
        self.obstacles.push(Obstacle::Rectangle(obstacle));
    }

    /// 添加任意类型障碍物
    pub fn add_obstacle_any(&mut self, obstacle: Obstacle) {
        self.obstacles.push(obstacle);
    }

    /// 检查区域是否包含障碍物 - 使用精确的几何检测
    /// 对于1x1网格，只要有任何重叠就标记为不可通行
    fn check_obstacle_in_region(region: &Rectangle, obstacles: &[Obstacle]) -> NodeType {
        // 对于1x1的最小网格，采用保守策略：只要有重叠就不可通行
        if region.width <= 1.0 && region.height <= 1.0 {
            for obstacle in obstacles {
                if obstacle.overlaps_with_rectangle(region) {
                    return NodeType::Blocked;
                }
            }
            return NodeType::Empty;
        }

        // 对于较大的网格，检查重叠情况来决定是否需要细分
        let mut has_overlap = false;
        let region_area = region.width * region.height;
        let mut total_overlap_area = 0.0;

        for obstacle in obstacles {
            if obstacle.overlaps_with_rectangle(region) {
                has_overlap = true;
                let overlap_area = obstacle.overlap_with_rectangle(region);
                total_overlap_area += overlap_area;
            }
        }

        if !has_overlap {
            NodeType::Empty
        } else if total_overlap_area >= region_area * 0.99 {
            NodeType::Blocked  // 几乎完全被覆盖
        } else {
            NodeType::Mixed    // 部分覆盖，需要细分
        }
    }

    /// 计算障碍物与矩形的重叠面积（兼容性方法）
    pub fn calculate_overlap(rect1: &Rectangle, obstacle: &Obstacle) -> f32 {
        obstacle.overlap_with_rectangle(rect1)
    }

    /// 构建自适应网格 - 使用分层规则网格
    pub fn build_adaptive_grid(&mut self) {
        self.cells.clear();
        self.adjacency.clear();

        // 从最粗的网格开始，递归细化需要细化的区域
        let max_level = (self.map_size as f32).log2() as u8;

        // 从整个地图开始递归细化
        let root_bounds = Rectangle::new(0.0, 0.0, self.map_size as f32, self.map_size as f32);
        self.subdivide_region(root_bounds, 0, max_level);

        // 为每个单元分配ID
        for (i, cell) in self.cells.iter_mut().enumerate() {
            cell.id = i;
        }

        // 构建邻接关系
        self.build_adjacency();
    }

    /// 递归细分区域 - 确保网格边界对齐
    fn subdivide_region(&mut self, bounds: Rectangle, level: u8, max_level: u8) {
        // 检查当前区域的类型
        let node_type = Self::check_obstacle_in_region(&bounds, &self.obstacles);

        // 如果是纯空旷或纯障碍区域，或者已达到最大深度，或者已经是1x1大小，则停止细分
        if node_type != NodeType::Mixed || level >= max_level || bounds.width <= 1.0 || bounds.height <= 1.0 {
            let cell = GridCell {
                id: 0, // 临时ID，稍后会重新分配
                bounds,
                level,
                node_type,
            };
            self.cells.push(cell);
            return;
        }

        // 如果是混合区域，则细分为4个子区域
        // 确保细分后的边界仍然是整数坐标
        let current_size = bounds.width as u32;
        let half_size = current_size / 2;

        // 只有当当前大小是偶数时才能均匀细分
        if current_size % 2 != 0 || half_size == 0 {
            // 如果无法均匀细分，则停止细分
            let cell = GridCell {
                id: 0, // 临时ID，稍后会重新分配
                bounds,
                level,
                node_type,
            };
            self.cells.push(cell);
            return;
        }

        let x = bounds.x as u32;
        let y = bounds.y as u32;

        let children = [
            Rectangle::new(x as f32, y as f32, half_size as f32, half_size as f32),
            Rectangle::new((x + half_size) as f32, y as f32, half_size as f32, half_size as f32),
            Rectangle::new(x as f32, (y + half_size) as f32, half_size as f32, half_size as f32),
            Rectangle::new((x + half_size) as f32, (y + half_size) as f32, half_size as f32, half_size as f32),
        ];

        // 递归处理每个子区域
        for child_bounds in children.iter() {
            self.subdivide_region(*child_bounds, level + 1, max_level);
        }
    }

    /// 构建邻接关系
    fn build_adjacency(&mut self) {
        self.adjacency.clear();

        for i in 0..self.cells.len() {
            let mut neighbors = Vec::new();

            for j in 0..self.cells.len() {
                if i != j && self.are_adjacent(i, j) {
                    let cost = self.calculate_edge_cost(i, j);
                    neighbors.push((j, cost));
                }
            }

            self.adjacency.insert(i, neighbors);
        }
    }

    /// 检查两个网格单元是否相邻
    fn are_adjacent(&self, cell_a_id: usize, cell_b_id: usize) -> bool {
        let cell_a = &self.cells[cell_a_id];
        let cell_b = &self.cells[cell_b_id];

        // 只有空旷单元才能通行
        if cell_a.node_type == NodeType::Blocked || cell_b.node_type == NodeType::Blocked {
            return false;
        }

        // 检查两个矩形是否共享边界
        let a = &cell_a.bounds;
        let b = &cell_b.bounds;

        // 检查水平相邻
        let horizontal_adjacent =
            (a.x + a.width == b.x || b.x + b.width == a.x) &&
            !(a.y + a.height <= b.y || b.y + b.height <= a.y);

        // 检查垂直相邻
        let vertical_adjacent =
            (a.y + a.height == b.y || b.y + b.height == a.y) &&
            !(a.x + a.width <= b.x || b.x + b.width <= a.x);

        horizontal_adjacent || vertical_adjacent
    }

    /// 计算两个相邻网格单元之间的边代价
    fn calculate_edge_cost(&self, cell_a_id: usize, cell_b_id: usize) -> f32 {
        let cell_a = &self.cells[cell_a_id];
        let cell_b = &self.cells[cell_b_id];

        let center_a = cell_a.bounds.center();
        let center_b = cell_b.bounds.center();

        // 使用欧几里得距离
        let dx = center_a.0 - center_b.0;
        let dy = center_a.1 - center_b.1;
        (dx * dx + dy * dy).sqrt()
    }

    /// 找到包含指定点的网格单元
    pub fn find_cell_containing_point(&self, x: f32, y: f32) -> Option<usize> {
        for (i, cell) in self.cells.iter().enumerate() {
            if cell.bounds.contains_point(x, y) && cell.node_type != NodeType::Blocked {
                return Some(i);
            }
        }
        None
    }

    /// A*路径规划算法
    pub fn find_path(&self, start_x: f32, start_y: f32, goal_x: f32, goal_y: f32) -> Option<Vec<usize>> {
        // 找到起点和终点所在的网格单元
        let start_cell = self.find_cell_containing_point(start_x, start_y)?;
        let goal_cell = self.find_cell_containing_point(goal_x, goal_y)?;

        if start_cell == goal_cell {
            return Some(vec![start_cell]);
        }

        let mut open_set = BinaryHeap::new();
        let mut closed_set = HashSet::new();
        let mut came_from: HashMap<usize, usize> = HashMap::new();
        let mut g_score: HashMap<usize, f32> = HashMap::new();

        // 初始化起点
        g_score.insert(start_cell, 0.0);
        let h_cost = self.heuristic(start_cell, goal_cell);
        open_set.push(PathNode {
            cell_id: start_cell,
            g_cost: 0.0,
            h_cost,
            parent: None,
        });

        while let Some(current_node) = open_set.pop() {
            let current_cell = current_node.cell_id;

            if current_cell == goal_cell {
                // 重建路径
                return Some(self.reconstruct_path(&came_from, current_cell));
            }

            closed_set.insert(current_cell);

            // 检查所有邻居
            if let Some(neighbors) = self.adjacency.get(&current_cell) {
                for &(neighbor_cell, edge_cost) in neighbors {
                    if closed_set.contains(&neighbor_cell) {
                        continue;
                    }

                    let tentative_g = current_node.g_cost + edge_cost;

                    let is_better = if let Some(&existing_g) = g_score.get(&neighbor_cell) {
                        tentative_g < existing_g
                    } else {
                        true
                    };

                    if is_better {
                        came_from.insert(neighbor_cell, current_cell);
                        g_score.insert(neighbor_cell, tentative_g);

                        let h_cost = self.heuristic(neighbor_cell, goal_cell);
                        open_set.push(PathNode {
                            cell_id: neighbor_cell,
                            g_cost: tentative_g,
                            h_cost,
                            parent: Some(current_cell),
                        });
                    }
                }
            }
        }

        None // 没有找到路径
    }

    /// 启发式函数（欧几里得距离）
    fn heuristic(&self, cell_a: usize, cell_b: usize) -> f32 {
        let center_a = self.cells[cell_a].bounds.center();
        let center_b = self.cells[cell_b].bounds.center();

        let dx = center_a.0 - center_b.0;
        let dy = center_a.1 - center_b.1;
        (dx * dx + dy * dy).sqrt()
    }

    /// 重建路径
    fn reconstruct_path(&self, came_from: &HashMap<usize, usize>, mut current: usize) -> Vec<usize> {
        let mut path = vec![current];

        while let Some(&parent) = came_from.get(&current) {
            current = parent;
            path.push(current);
        }

        path.reverse();
        path
    }

    /// 动态添加障碍物并局部更新网格
    pub fn add_obstacle_dynamic(&mut self, new_obstacle: Rectangle) -> std::time::Duration {
        let start_time = std::time::Instant::now();

        // 1. 添加新障碍物
        let obstacle = Obstacle::Rectangle(new_obstacle);
        self.obstacles.push(obstacle.clone());

        // 2. 找到受影响的网格单元
        let mut affected_cells = Vec::new();
        for (i, cell) in self.cells.iter().enumerate() {
            let overlap = Self::calculate_overlap(&cell.bounds, &obstacle);
            if overlap > 0.0 {
                affected_cells.push(i);
            }
        }

        println!("新障碍物影响了 {} 个网格单元", affected_cells.len());

        // 3. 更新受影响单元的类型
        for &cell_id in &affected_cells {
            let cell = &mut self.cells[cell_id];
            cell.node_type = Self::check_obstacle_in_region(&cell.bounds, &self.obstacles);
        }

        // 4. 检查是否需要细分受影响的单元
        let mut cells_to_subdivide = Vec::new();
        for &cell_id in &affected_cells {
            let cell = &self.cells[cell_id];
            if cell.node_type == NodeType::Mixed && cell.bounds.width > 1.0 {
                cells_to_subdivide.push(cell_id);
            }
        }

        // 5. 细分混合类型的单元
        if !cells_to_subdivide.is_empty() {
            println!("需要细分 {} 个混合单元", cells_to_subdivide.len());

            // 从后往前删除，避免索引问题
            cells_to_subdivide.sort_by(|a, b| b.cmp(a));

            for &cell_id in &cells_to_subdivide {
                let cell = self.cells.remove(cell_id);
                self.subdivide_cell_locally(cell);
            }
        }

        // 6. 重新分配ID
        for (i, cell) in self.cells.iter_mut().enumerate() {
            cell.id = i;
        }

        // 7. 局部重建邻接关系（只重建受影响的部分）
        self.rebuild_local_adjacency(&affected_cells);

        start_time.elapsed()
    }

    /// 局部细分单个网格单元
    fn subdivide_cell_locally(&mut self, cell: GridCell) {
        let max_level = (self.map_size as f32).log2() as u8;

        // 如果已经是最小单元或者不是混合类型，直接添加回去
        if cell.bounds.width <= 1.0 || cell.node_type != NodeType::Mixed {
            self.cells.push(cell);
            return;
        }

        // 细分为4个子单元
        let current_size = cell.bounds.width as u32;
        let half_size = current_size / 2;

        if current_size % 2 != 0 || half_size == 0 {
            self.cells.push(cell);
            return;
        }

        let x = cell.bounds.x as u32;
        let y = cell.bounds.y as u32;

        let children_bounds = [
            Rectangle::new(x as f32, y as f32, half_size as f32, half_size as f32),
            Rectangle::new((x + half_size) as f32, y as f32, half_size as f32, half_size as f32),
            Rectangle::new(x as f32, (y + half_size) as f32, half_size as f32, half_size as f32),
            Rectangle::new((x + half_size) as f32, (y + half_size) as f32, half_size as f32, half_size as f32),
        ];

        for child_bounds in children_bounds.iter() {
            let node_type = Self::check_obstacle_in_region(child_bounds, &self.obstacles);
            let child_cell = GridCell {
                id: 0, // 临时ID
                bounds: *child_bounds,
                level: cell.level + 1,
                node_type,
            };

            // 如果子单元仍然是混合类型且可以继续细分，递归细分
            if node_type == NodeType::Mixed && child_bounds.width > 1.0 && cell.level + 1 < max_level {
                self.subdivide_cell_locally(child_cell);
            } else {
                self.cells.push(child_cell);
            }
        }
    }

    /// 局部重建邻接关系
    fn rebuild_local_adjacency(&mut self, _affected_cell_ids: &[usize]) {
        // 简化版本：重建所有邻接关系
        // 在实际应用中，可以只重建受影响单元及其邻居的关系
        self.build_adjacency();
    }
}

/// 可视化模块
pub struct GridVisualizer {
    pub image_size: u32,
    pub scale: f32,
}

impl GridVisualizer {
    pub fn new(image_size: u32, map_size: f32) -> Self {
        Self {
            image_size,
            scale: image_size as f32 / map_size,
        }
    }

    /// 将世界坐标转换为图像坐标
    fn world_to_image(&self, x: f32, y: f32) -> (i32, i32) {
        let img_x = (x * self.scale) as i32;
        let img_y = (y * self.scale) as i32;
        (img_x, img_y)
    }

    /// 绘制网格可视化图像
    pub fn visualize_grid(&self, grid: &MultiResolutionGrid) -> RgbImage {
        self.visualize_grid_with_path(grid, None, None, None)
    }

    /// 绘制带路径的网格可视化图像
    pub fn visualize_grid_with_path(
        &self,
        grid: &MultiResolutionGrid,
        path: Option<&[usize]>,
        start_point: Option<(f32, f32)>,
        goal_point: Option<(f32, f32)>
    ) -> RgbImage {
        let mut image = ImageBuffer::new(self.image_size, self.image_size);

        // 填充白色背景
        for pixel in image.pixels_mut() {
            *pixel = Rgb([255, 255, 255]);
        }

        // 绘制1x1背景网格（非常淡的灰色边框）
        // 对于大地图，使用更淡的颜色和稀疏采样
        let grid_color = if grid.map_size > 64 { [240, 240, 240] } else { [220, 220, 220] };
        let step = if grid.map_size > 64 { 4 } else { 1 }; // 大地图时每4个单位画一条线

        for y in (0..grid.map_size).step_by(step as usize) {
            for x in (0..grid.map_size).step_by(step as usize) {
                let cell_bounds = Rectangle::new(x as f32, y as f32, step as f32, step as f32);
                self.draw_rectangle_border(&mut image, &cell_bounds, grid_color);
            }
        }

        // 绘制障碍物（黑色填充）
        for obstacle in &grid.obstacles {
            self.draw_obstacle(&mut image, obstacle, [0, 0, 0]);
        }

        // 绘制所有网格单元的边框（较粗的边框）
        for cell in &grid.cells {
            let color = match cell.node_type {
                NodeType::Empty => [0, 255, 0],    // 绿色边框 - 空旷区域
                NodeType::Blocked => [255, 0, 0],  // 红色边框 - 障碍区域
                NodeType::Mixed => [0, 0, 255],    // 蓝色边框 - 混合区域
            };

            // 绘制较粗的边框
            self.draw_thick_rectangle_border(&mut image, &cell.bounds, color);
        }

        // 绘制完整路径（包括起点到第一个网格、最后一个网格到终点的连接）
        if let (Some(path_cells), Some(start), Some(goal)) = (path, start_point, goal_point) {
            self.draw_complete_path(&mut image, grid, path_cells, start, goal);
        }

        // 绘制起点和终点
        if let Some((start_x, start_y)) = start_point {
            self.draw_point(&mut image, start_x, start_y, [255, 255, 0], 6); // 黄色起点
        }

        if let Some((goal_x, goal_y)) = goal_point {
            self.draw_point(&mut image, goal_x, goal_y, [255, 0, 255], 6); // 紫色终点
        }

        image
    }

    /// 绘制填充矩形
    fn draw_filled_rectangle(&self, image: &mut RgbImage, rect: &Rectangle, color: [u8; 3]) {
        let (x1, y1) = self.world_to_image(rect.x, rect.y);
        let (x2, y2) = self.world_to_image(rect.x + rect.width, rect.y + rect.height);

        let width = (x2 - x1).max(1) as u32;
        let height = (y2 - y1).max(1) as u32;

        if x1 >= 0 && y1 >= 0 && x1 < self.image_size as i32 && y1 < self.image_size as i32 {
            let rect = Rect::at(x1, y1).of_size(width, height);
            draw_filled_rect_mut(image, rect, Rgb(color));
        }
    }

    /// 绘制矩形边框
    fn draw_rectangle_border(&self, image: &mut RgbImage, rect: &Rectangle, color: [u8; 3]) {
        let (x1, y1) = self.world_to_image(rect.x, rect.y);
        let (x2, y2) = self.world_to_image(rect.x + rect.width, rect.y + rect.height);

        let width = (x2 - x1).max(1) as u32;
        let height = (y2 - y1).max(1) as u32;

        if x1 >= 0 && y1 >= 0 && x1 < self.image_size as i32 && y1 < self.image_size as i32 {
            let rect = Rect::at(x1, y1).of_size(width, height);
            draw_hollow_rect_mut(image, rect, Rgb(color));
        }
    }

    /// 绘制较粗的矩形边框
    fn draw_thick_rectangle_border(&self, image: &mut RgbImage, rect: &Rectangle, color: [u8; 3]) {
        let (x1, y1) = self.world_to_image(rect.x, rect.y);
        let (x2, y2) = self.world_to_image(rect.x + rect.width, rect.y + rect.height);

        let width = (x2 - x1).max(1) as u32;
        let height = (y2 - y1).max(1) as u32;

        if x1 >= 0 && y1 >= 0 && x1 < self.image_size as i32 && y1 < self.image_size as i32 {
            let rect = Rect::at(x1, y1).of_size(width, height);
            // 绘制多层边框来实现粗边框效果
            draw_hollow_rect_mut(image, rect, Rgb(color));
            if width > 2 && height > 2 {
                let inner_rect = Rect::at(x1 + 1, y1 + 1).of_size(width - 2, height - 2);
                draw_hollow_rect_mut(image, inner_rect, Rgb(color));
            }
        }
    }

    /// 绘制路径
    fn draw_path(&self, image: &mut RgbImage, grid: &MultiResolutionGrid, path: &[usize]) {
        if path.is_empty() {
            return;
        }

        // 如果只有一个网格单元，不需要绘制路径
        if path.len() == 1 {
            return;
        }

        // 绘制网格中心之间的连接
        for i in 0..path.len() - 1 {
            let current_cell = &grid.cells[path[i]];
            let next_cell = &grid.cells[path[i + 1]];

            let current_center = current_cell.bounds.center();
            let next_center = next_cell.bounds.center();

            self.draw_thick_line(image, current_center, next_center, [255, 165, 0]); // 橙色路径
        }
    }

    /// 绘制完整路径（包括起点到第一个网格中心，最后一个网格中心到终点）
    fn draw_complete_path(
        &self,
        image: &mut RgbImage,
        grid: &MultiResolutionGrid,
        path: &[usize],
        start_point: (f32, f32),
        goal_point: (f32, f32)
    ) {
        if path.is_empty() {
            return;
        }

        if path.len() == 1 {
            // 如果起点和终点在同一个网格中，直接连接
            self.draw_thick_line(image, start_point, goal_point, [255, 165, 0]);
            return;
        }

        // 1. 绘制起点到第一个网格中心的连接
        let first_cell = &grid.cells[path[0]];
        let first_center = first_cell.bounds.center();
        self.draw_thick_line(image, start_point, first_center, [255, 165, 0]);

        // 2. 绘制网格中心之间的连接
        for i in 0..path.len() - 1 {
            let current_cell = &grid.cells[path[i]];
            let next_cell = &grid.cells[path[i + 1]];

            let current_center = current_cell.bounds.center();
            let next_center = next_cell.bounds.center();

            self.draw_thick_line(image, current_center, next_center, [255, 165, 0]);
        }

        // 3. 绘制最后一个网格中心到终点的连接
        let last_cell = &grid.cells[path[path.len() - 1]];
        let last_center = last_cell.bounds.center();
        self.draw_thick_line(image, last_center, goal_point, [255, 165, 0]);
    }

    /// 绘制粗线条
    fn draw_thick_line(&self, image: &mut RgbImage, start: (f32, f32), end: (f32, f32), color: [u8; 3]) {
        let (x1, y1) = self.world_to_image(start.0, start.1);
        let (x2, y2) = self.world_to_image(end.0, end.1);

        // 绘制主线条
        draw_line_segment_mut(image, (x1 as f32, y1 as f32), (x2 as f32, y2 as f32), Rgb(color));

        // 绘制更粗的路径（通过绘制多条相邻的线）
        for dx in -1..=1 {
            for dy in -1..=1 {
                if dx != 0 || dy != 0 {
                    draw_line_segment_mut(
                        image,
                        ((x1 + dx) as f32, (y1 + dy) as f32),
                        ((x2 + dx) as f32, (y2 + dy) as f32),
                        Rgb(color)
                    );
                }
            }
        }
    }

    /// 绘制点（起点或终点）
    fn draw_point(&self, image: &mut RgbImage, x: f32, y: f32, color: [u8; 3], radius: i32) {
        let (img_x, img_y) = self.world_to_image(x, y);

        // 绘制实心圆
        for dy in -radius..=radius {
            for dx in -radius..=radius {
                if dx * dx + dy * dy <= radius * radius {
                    let px = img_x + dx;
                    let py = img_y + dy;

                    if px >= 0 && py >= 0 && px < self.image_size as i32 && py < self.image_size as i32 {
                        image.put_pixel(px as u32, py as u32, Rgb(color));
                    }
                }
            }
        }
    }

    /// 绘制障碍物（支持不同类型）
    fn draw_obstacle(&self, image: &mut RgbImage, obstacle: &Obstacle, color: [u8; 3]) {
        match obstacle {
            Obstacle::Rectangle(rect) => {
                self.draw_filled_rectangle(image, rect, color);
            }
            Obstacle::Circle { center, radius } => {
                self.draw_filled_circle(image, center, *radius, color);
            }
            Obstacle::Polygon { vertices } => {
                self.draw_filled_polygon(image, vertices, color);
            }
            Obstacle::PointSet { points, radius } => {
                for point in points {
                    self.draw_filled_circle(image, point, *radius, color);
                }
            }
        }
    }

    /// 绘制填充圆形
    fn draw_filled_circle(&self, image: &mut RgbImage, center: &Point, radius: f32, color: [u8; 3]) {
        let (center_x, center_y) = self.world_to_image(center.x, center.y);
        let img_radius = (radius * self.scale) as i32;

        for dy in -img_radius..=img_radius {
            for dx in -img_radius..=img_radius {
                if dx * dx + dy * dy <= img_radius * img_radius {
                    let px = center_x + dx;
                    let py = center_y + dy;

                    if px >= 0 && py >= 0 && px < self.image_size as i32 && py < self.image_size as i32 {
                        image.put_pixel(px as u32, py as u32, Rgb(color));
                    }
                }
            }
        }
    }

    /// 绘制填充多边形（简化版本）
    fn draw_filled_polygon(&self, image: &mut RgbImage, vertices: &[Point], color: [u8; 3]) {
        if vertices.len() < 3 {
            return;
        }

        // 找到边界框
        let min_x = vertices.iter().map(|p| p.x).fold(f32::INFINITY, f32::min);
        let max_x = vertices.iter().map(|p| p.x).fold(f32::NEG_INFINITY, f32::max);
        let min_y = vertices.iter().map(|p| p.y).fold(f32::INFINITY, f32::min);
        let max_y = vertices.iter().map(|p| p.y).fold(f32::NEG_INFINITY, f32::max);

        let (start_x, start_y) = self.world_to_image(min_x, min_y);
        let (end_x, end_y) = self.world_to_image(max_x, max_y);

        // 扫描填充
        for py in start_y..=end_y {
            for px in start_x..=end_x {
                if px >= 0 && py >= 0 && px < self.image_size as i32 && py < self.image_size as i32 {
                    let world_x = px as f32 / self.scale;
                    let world_y = py as f32 / self.scale;

                    if Obstacle::point_in_polygon(world_x, world_y, vertices) {
                        image.put_pixel(px as u32, py as u32, Rgb(color));
                    }
                }
            }
        }
    }
}

fn main() {
    println!("开始创建20x20x20三维多分辨率网格测试...");

    // 创建20x20x20的3D网格系统（小规模测试）
    let mut grid_3d = MultiResolutionGrid3D::new(32);

    // 添加3D障碍物到20x20x20地图上
    println!("添加3D障碍物到20x20x20地图:");

    // 1. 长方体障碍物（建筑物）- 只添加一个
    let box_obstacle = Box3D::new(5.0, 5.0, 0.0, 3.0, 4.0, 16.0);  // 高度16，占据一半
    grid_3d.add_obstacle(Obstacle3D::Box(box_obstacle));
    println!("  长方体障碍物: x={:.0}, y={:.0}, z={:.0}, 尺寸={}x{}x{}",
             box_obstacle.x, box_obstacle.y, box_obstacle.z,
             box_obstacle.width, box_obstacle.height, box_obstacle.depth);

    // 2. 圆柱体障碍物（塔楼）- 只添加一个
    let cylinder_center = Point3D::new(12.0, 8.0, 0.0);
    let cylinder_radius = 2.0;
    let cylinder_height = 18.0;  // 高度18，超过一半
    grid_3d.add_obstacle(Obstacle3D::Cylinder {
        center: cylinder_center,
        radius: cylinder_radius,
        height: cylinder_height
    });
    println!("  圆柱体障碍物: 中心({}, {}, {}), 半径={}, 高度={}",
             cylinder_center.x, cylinder_center.y, cylinder_center.z,
             cylinder_radius, cylinder_height);

    // 3. 棱柱障碍物（不规则建筑）- 只添加一个三角形棱柱
    let prism_vertices = vec![
        Point::new(15.0, 12.0),
        Point::new(18.0, 16.0),
        Point::new(12.0, 16.0),
    ];
    let prism_bottom = 0.0;
    let prism_height = 20.0;  // 高度20，超过一半
    grid_3d.add_obstacle(Obstacle3D::Prism {
        vertices: prism_vertices.clone(),
        bottom: prism_bottom,
        height: prism_height
    });
    println!("  棱柱障碍物: {}个顶点, 底部高度={}, 高度={}",
             prism_vertices.len(), prism_bottom, prism_height);

    // 4. 3D点集障碍物（山体、岩石群）- 只添加几个点
    let pointset_3d = vec![
        Point3D::new(8.0, 15.0, 8.0),   // 提高Z坐标
        Point3D::new(10.0, 17.0, 12.0),
        Point3D::new(6.0, 18.0, 6.0),
    ];

    grid_3d.add_obstacle(Obstacle3D::PointSet {
        points: pointset_3d.clone(),
        radius: 2.0  // 增大半径
    });
    println!("  3D点集障碍物: {}个点，每个半径2.0", pointset_3d.len());

    println!("地图大小: {}x{}x{}", grid_3d.map_size, grid_3d.map_size, grid_3d.map_size);
    println!("最小网格大小: 1x1x1 单位");

    // 构建3D自适应网格
    println!("\n构建3D自适应网格...");
    let grid_start_time = Instant::now();
    grid_3d.build_adaptive_grid();
    let grid_duration = grid_start_time.elapsed();
    println!("3D网格构建完成，耗时: {:.2?}", grid_duration);

    // 调试：检查网格构建结果
    println!("调试 - 3D网格构建结果:");
    println!("  总网格单元数: {}", grid_3d.cells.len());
    if grid_3d.cells.len() > 0 {
        println!("  前5个网格单元:");
        for (i, cell) in grid_3d.cells.iter().take(5).enumerate() {
            println!("    单元{}: 位置({:.0},{:.0},{:.0}) 大小({:.0}x{:.0}x{:.0}) 层级={} 类型={:?}",
                     i, cell.bounds.x, cell.bounds.y, cell.bounds.z,
                     cell.bounds.width, cell.bounds.height, cell.bounds.depth,
                     cell.level, cell.node_type);
        }
    } else {
        println!("  警告：没有生成任何网格单元！");
    }

    // 调试：检查所有障碍物区域的网格
    println!("调试信息:");

    // 检查与任意障碍物重叠的网格单元
    let mut obstacle_cells = 0;
    for cell in &grid_3d.cells {
        let mut total_overlap = 0.0;
        for obstacle in &grid_3d.obstacles {
            let overlap_volume = obstacle.overlap_with_box(&cell.bounds);
            total_overlap += overlap_volume;
        }

        if total_overlap > 0.0 {
            obstacle_cells += 1;
            if obstacle_cells <= 10 { // 只显示前10个，避免输出过多
                println!("与障碍物重叠的单元: ({}, {}, {}, {}x{}x{}) 类型={:?} 重叠体积={:.1}",
                         cell.bounds.x, cell.bounds.y, cell.bounds.z,
                         cell.bounds.width, cell.bounds.height, cell.bounds.depth,
                         cell.node_type, total_overlap);
            }
        }
    }
    println!("与障碍物重叠的网格单元总数: {}", obstacle_cells);

    // 3D路径规划测试
    println!("\n开始3D路径规划测试...");
    let start_point = (2.0, 2.0, 5.0);     // 起点：左下角空旷区域
    let goal_point = (18.0, 18.0, 5.0);    // 终点：右上角空旷区域

    println!("起点: ({}, {}, {})", start_point.0, start_point.1, start_point.2);
    println!("终点: ({}, {}, {})", goal_point.0, goal_point.1, goal_point.2);

    // 路径规划计时
    let pathfinding_start_time = Instant::now();
    let path = grid_3d.find_path(start_point.0, start_point.1, start_point.2,
                                 goal_point.0, goal_point.1, goal_point.2);
    let pathfinding_duration = pathfinding_start_time.elapsed();

    println!("3D路径规划完成，耗时: {:.2?}", pathfinding_duration);

    match &path {
        Some(path_cells) => {
            println!("找到3D路径！路径长度: {} 个网格单元", path_cells.len());
            println!("路径经过的网格单元:");
            for (i, &cell_id) in path_cells.iter().enumerate() {
                let cell = &grid_3d.cells[cell_id];
                let center = cell.bounds.center();
                println!("  步骤 {}: 单元{} 中心({:.1}, {:.1}, {:.1}) 大小({}x{}x{}) 层级={}",
                         i + 1, cell_id,
                         center.0, center.1, center.2,
                         cell.bounds.width, cell.bounds.height, cell.bounds.depth,
                         cell.level);

                // 显示邻居信息
                if let Some(neighbors) = grid_3d.adjacency.get(&cell_id) {
                    println!("    邻居数量: {}", neighbors.len());
                    if neighbors.len() < 10 { // 只显示少量邻居，避免输出过多
                        for (neighbor_id, cost) in neighbors {
                            let neighbor_cell = &grid_3d.cells[*neighbor_id];
                            let neighbor_center = neighbor_cell.bounds.center();
                            println!("      -> 单元{} 中心({:.1}, {:.1}, {:.1}) 代价={:.2}",
                                   neighbor_id, neighbor_center.0, neighbor_center.1, neighbor_center.2, cost);
                        }
                    }
                }
            }
        }
        None => {
            println!("未找到3D路径！");
        }
    }

    // 统计3D网格单元信息
    let mut empty_count = 0;
    let mut blocked_count = 0;
    let mut mixed_count = 0;
    let mut level_counts = HashMap::new();

    for cell in &grid_3d.cells {
        match cell.node_type {
            NodeType::Empty => empty_count += 1,
            NodeType::Blocked => blocked_count += 1,
            NodeType::Mixed => mixed_count += 1,
        }

        *level_counts.entry(cell.level).or_insert(0) += 1;
    }

    println!("3D网格统计信息:");
    println!("  总网格单元数: {}", grid_3d.cells.len());
    println!("  空旷单元: {}", empty_count);
    println!("  障碍单元: {}", blocked_count);
    println!("  混合单元: {}", mixed_count);

    println!("  各层级单元数:");
    for (level, count) in level_counts.iter() {
        let cell_size = 2_u32.pow(*level as u32);
        println!("    层级{} ({}x{}x{}单元): {} 个", level, cell_size, cell_size, cell_size, count);
    }

    // 导出3D体素数据用于Python可视化
    println!("\n导出3D体素数据...");
    let export_start_time = Instant::now();

    // 获取体素数据并添加路径点
    let mut voxel_data = grid_3d.export_voxel_data();

    // 如果找到了路径，将路径点添加到导出数据中
    if let Some(ref path_cells) = path {
        let mut path_points = Vec::new();
        for &cell_id in path_cells {
            let cell = &grid_3d.cells[cell_id];
            let center = cell.bounds.center();
            path_points.push(Point3D::new(center.0, center.1, center.2));
        }
        voxel_data.path_points = Some(path_points);
        println!("路径点已添加到导出数据: {} 个点", path_cells.len());
    }

    // 保存到文件
    let json_string = serde_json::to_string_pretty(&voxel_data).unwrap();
    match std::fs::write("voxel_data_3d.json", json_string) {
        Ok(_) => {
            let export_duration = export_start_time.elapsed();
            println!("3D体素数据已导出到 voxel_data_3d.json，耗时: {:.2?}", export_duration);
            println!("请使用Python脚本进行3D可视化");
        }
        Err(e) => {
            println!("导出体素数据失败: {}", e);
        }
    }

    // 打印一些3D网格单元的详细信息
    println!("\n前10个3D网格单元的详细信息:");
    for (i, cell) in grid_3d.cells.iter().take(10).enumerate() {
        println!("单元 {}: 层级={}, 类型={:?}, 边界=({:.0}, {:.0}, {:.0}, {}x{}x{})",
                 i + 1,
                 cell.level,
                 cell.node_type,
                 cell.bounds.x,
                 cell.bounds.y,
                 cell.bounds.z,
                 cell.bounds.width,
                 cell.bounds.height,
                 cell.bounds.depth);
    }

    println!("\n=== 3D性能统计 ===");
    println!("地图大小: {}x{}x{}", grid_3d.map_size, grid_3d.map_size, grid_3d.map_size);
    println!("障碍物数量: {}", grid_3d.obstacles.len());
    println!("网格单元总数: {}", grid_3d.cells.len());
    println!("网格构建时间: {:.2?}", grid_duration);
    println!("路径规划时间: {:.2?}", pathfinding_duration);

    let total_time = grid_duration + pathfinding_duration;
    println!("总耗时: {:.2?}", total_time);

    // 计算3D网格压缩率
    let total_possible_cells = (grid_3d.map_size * grid_3d.map_size * grid_3d.map_size) as f32;
    let compression_ratio = (total_possible_cells - grid_3d.cells.len() as f32) / total_possible_cells * 100.0;
    println!("3D网格压缩率: {:.1}% (从{}个1x1x1网格压缩到{}个多分辨率网格)",
             compression_ratio, total_possible_cells as u32, grid_3d.cells.len());

    println!("\n=== 3D测试完成 ===");
    println!("3D多分辨率网格系统测试完成！");
    println!("数据已导出到 voxel_data_3d.json");
    println!("\n请使用以下Python脚本进行3D可视化:");
    println!("python visualize_3d.py");
    println!("\n可视化说明:");
    println!("  - 绿色体素: 空旷区域的网格单元");
    println!("  - 红色体素: 被障碍物占据的网格单元");
    println!("  - 黑色体素: 障碍物本身");
    println!("  - 网格边框: 显示多分辨率网格的边界，用于验证网格划分准确性");
}
