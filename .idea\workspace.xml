<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="CargoProjects">
    <cargoProject FILE="$PROJECT_DIR$/Cargo.toml">
      <package file="$PROJECT_DIR$">
        <enabledFeature name="default" />
      </package>
    </cargoProject>
  </component>
  <component name="ChangeListManager">
    <list default="true" id="854d443b-fe88-47be-9bbe-9b02ac349026" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="RsBuildProfile:dev" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MacroExpansionManager">
    <option name="directoryName" value="Jt3O3amP" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2zwb7QeBxvnvlTCJGXgIrokVhTK" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Cargo.Run adaptive_pathfinding.executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.rust.reset.selective.auto.import": "true",
    "git-widget-placeholder": "master",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "D:/crscu/route_palnning/codes/rust/adaptive_pathfinding",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "org.rust.cargo.project.model.PROJECT_DISCOVERY": "true",
    "org.rust.cargo.project.model.impl.CargoExternalSystemProjectAware.subscribe.first.balloon": "",
    "org.rust.first.attach.projects": "true",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="Cargo.Run adaptive_pathfinding">
    <configuration name="Run adaptive_pathfinding" type="CargoCommandRunConfiguration" factoryName="Cargo Command">
      <option name="buildProfileId" value="dev" />
      <option name="command" value="run --package adaptive_pathfinding --bin adaptive_pathfinding" />
      <option name="workingDirectory" value="file://$PROJECT_DIR$" />
      <envs />
      <option name="emulateTerminal" value="true" />
      <option name="channel" value="DEFAULT" />
      <option name="requiredFeatures" value="true" />
      <option name="allFeatures" value="false" />
      <option name="withSudo" value="false" />
      <option name="buildTarget" value="REMOTE" />
      <option name="backtrace" value="SHORT" />
      <option name="isRedirectInput" value="false" />
      <option name="redirectInputPath" value="" />
      <method v="2">
        <option name="CARGO.BUILD_TASK_PROVIDER" enabled="true" />
      </method>
    </configuration>
    <configuration name="Test adaptive_pathfinding" type="CargoCommandRunConfiguration" factoryName="Cargo Command">
      <option name="command" value="test --workspace" />
      <option name="workingDirectory" value="file://$PROJECT_DIR$" />
      <envs />
      <option name="emulateTerminal" value="true" />
      <option name="channel" value="DEFAULT" />
      <option name="requiredFeatures" value="true" />
      <option name="allFeatures" value="false" />
      <option name="withSudo" value="false" />
      <option name="buildTarget" value="REMOTE" />
      <option name="backtrace" value="SHORT" />
      <option name="isRedirectInput" value="false" />
      <option name="redirectInputPath" value="" />
      <method v="2">
        <option name="CARGO.BUILD_TASK_PROVIDER" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="RustProjectSettings">
    <option name="toolchainHomeDirectory" value="$USER_HOME$/.cargo/bin" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="854d443b-fe88-47be-9bbe-9b02ac349026" name="更改" comment="" />
      <created>1752644914408</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752644914408</updated>
      <workItem from="1752644915490" duration="9146000" />
      <workItem from="1752712622926" duration="9802000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>